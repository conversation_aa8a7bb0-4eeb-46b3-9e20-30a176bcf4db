@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Color Scheme - Pink/Rose Theme from Skills Component */
:root {
  /* Primary Colors */
  --primary-pink: #db2777; /* pink-600 */
  --primary-pink-dark: #be185d; /* pink-700 */
  --primary-pink-light: #f9a8d4; /* pink-300 */
  --primary-pink-lighter: #fce7f3; /* pink-100 */
  --primary-pink-lightest: #fdf2f8; /* pink-50 */

  /* Rose Colors */
  --rose-light: #fef7f7; /* rose-50 */
  --rose-lighter: #ffe4e6; /* rose-100 */

  /* Accent Colors */
  --accent-pink: #f472b6; /* pink-400 */
  --border-pink: #fbcfe8; /* pink-200 */
  --border-pink-dark: #f9a8d4; /* pink-300 */

  /* Background Gradients */
  --bg-gradient-start: var(--primary-pink-lightest);
  --bg-gradient-middle: #ffffff;
  --bg-gradient-end: var(--rose-lighter);

  /* Shadow Colors */
  --shadow-pink: rgba(251, 207, 232, 0.5); /* pink-200 with opacity */

  /* Text Colors */
  --text-primary: var(--primary-pink);
  --text-secondary: var(--primary-pink-dark);
}

/* Global Font and Base Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Apply global pink theme colors and prevent horizontal scroll */
html, body {
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-width: 100vw;   /* Ensure content doesn't exceed viewport width */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

body {
  color: var(--text-secondary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Global utility classes for consistent theming */
.theme-bg-gradient {
  background: linear-gradient(to bottom right, var(--bg-gradient-start), var(--bg-gradient-middle), var(--bg-gradient-end));
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-border {
  border-color: var(--border-pink);
}

.theme-bg-light {
  background-color: var(--primary-pink-lighter);
}

.theme-shadow {
  box-shadow: 0 4px 6px -1px var(--shadow-pink);
}

/* Global font utilities for consistent typography */
.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Ensure all headings use consistent font family */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Ensure all text elements use consistent font family */
p, span, div, a, button, input, textarea, label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}
